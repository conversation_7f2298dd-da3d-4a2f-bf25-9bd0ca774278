import gradio as gr
from tools.review import ai_review
from tools.gen_usecase import ai_case
from tools.regress import gen_regress_case
from tools.bug_recall import BugRecallTool

# 初始化bug召回工具
bug_recall_tool = BugRecallTool()

def gen_bug_recall(history, story_url):
    """
    Bug召回的Gradio接口函数
    """
    history.append({"role": "user", "content": f"帮我分析需求文档，召回相关BUG～"})
    yield history, None

    try:
        result = bug_recall_tool.recall_bugs_by_story_url(story_url)
        if result:
            history.append({"role": "assistant", "content": f"## Bug召回完成！\n\n**需求名称**: {result['story_info']['name']}\n\n**相关Bug数量**: {len(result['relevant_bugs'])}\n\n**建议测试用例数量**: {len(result['suggested_test_cases'])}"})
            yield history, result.get('file_path', None)
        else:
            history.append({"role": "assistant", "content": "Bug召回失败，请检查需求链接是否正确或稍后重试"})
            yield history, None
    except Exception as e:
        history.append({"role": "assistant", "content": f"Bug召回过程中发生错误：{str(e)}"})
        yield history, None

with gr.Blocks(title="AI用例系统", theme=gr.themes.Soft(font=[gr.themes.GoogleFont("Inconsolata"), "Arial", "sans-serif"])) as demo:
    gr.Markdown("# 🧠 AI用例系统")
    
    with gr.Tabs():
        with gr.TabItem("RAG回归用例"):
            gr.Markdown("上传需求URL，基于知识库获取回归用例（当前支持健康业务和 Sass 业务）")
            with gr.Row():
                with gr.Column(scale=1):
                    regress_url = gr.Textbox(
                        label="TAPD需求链接",
                        placeholder="请输入完整的TAPD需求URL...",
                        lines=2
                    )
                    knowledge_base = gr.Radio(
                        label="知识库",
                        choices=["SAAS用例","健康 checklist 用例", "健康增量用例"],
                        value="SAAS用例"
                    )
                    regress_btn = gr.Button("开始生成", variant="primary")
                    # 示例输入
                    gr.Examples(
                        examples=[
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472119919229"],
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122722991"],
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472121659512"],
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472119919425"],
                        ],
                        inputs=[regress_url],
                    )
                    
                with gr.Column(scale=2):
                    regress_chatbot = gr.Chatbot(
                        label="回归用例生成",
                        avatar_images=("resources/img/user.jpeg", "resources/img/bot.jpeg"),
                        height=700,
                        show_share_button=True,
                        type="messages",
                    )
                    regress_result = gr.File(
                        label="回归用例",
                        height=50
                    )
            
            regress_btn.click(
                fn=gen_regress_case,
                inputs=[regress_chatbot, regress_url, knowledge_base],
                outputs=[regress_chatbot, regress_result]
            )

        with gr.TabItem("用例评审"):
            gr.Markdown("上传用例文件并输入需求URL，获取AI评审意见")
            with gr.Row():
                with gr.Column(scale=1):
                    usecase_file = gr.File(
                        label="上传用例文件",
                        file_types=[".xmind", ".xlsx", ".xls"],
                        type="filepath",
                        height=150
                    )
                    review_url = gr.Textbox(
                        label="TAPD需求链接",
                        placeholder="请输入完整的TAPD需求URL...",
                        lines=2
                    )
                    review_btn = gr.Button("开始评审", variant="primary")
                    # 示例输入
                    gr.Examples(
                        examples=[
                            
                        ],
                        inputs=[usecase_file, review_url],
                    )
                    
                with gr.Column(scale=2):
                    review_chatbot = gr.Chatbot(
                        label="评审对话",
                        avatar_images=("resources/img/user.jpeg", "resources/img/bot.jpeg"),
                        height=700,
                        show_share_button=True,
                        type="messages",
                    )
                    review_result = gr.File(
                        label="评审结果",
                        height=50
                    )
            
            review_btn.click(
                fn=ai_review,
                inputs=[review_chatbot, usecase_file, review_url],
                outputs=[review_chatbot, review_result]
            )

        with gr.TabItem("用例生成"):
            gr.Markdown("上传需求URL，获取推荐用例")
            with gr.Row():
                with gr.Column(scale=1):
                    generate_url = gr.Textbox(
                        label="TAPD需求链接",
                        placeholder="请输入完整的TAPD需求URL...",
                        lines=2
                    )
                    generate_btn = gr.Button("开始生成", variant="primary")
                    # 示例输入
                    gr.Examples(
                        examples=[
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122143306"],
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122722991"],
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472121659512"],
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472119919425"],
                            []
                        ],
                        inputs=[generate_url],
                    )
                    
                with gr.Column(scale=2):
                    generate_chatbot = gr.Chatbot(
                        label="生成用例",
                        avatar_images=("resources/img/user.jpeg", "resources/img/bot.jpeg"),
                        height=700,
                        show_share_button=True,
                        type="messages",
                    )
                    generate_result = gr.File(
                        label="生成用例",
                        height=50
                    )
            
            generate_btn.click(
                fn=ai_case,
                inputs=[generate_chatbot, generate_url],
                outputs=[generate_chatbot, generate_result]
            )

        with gr.TabItem("Bug召回"):
            gr.Markdown("根据需求链接召回相关BUG，分析风险并生成针对性测试用例")
            with gr.Row():
                with gr.Column(scale=1):
                    bug_recall_url = gr.Textbox(
                        label="TAPD需求链接",
                        placeholder="请输入完整的TAPD需求URL...",
                        lines=2
                    )
                    bug_recall_btn = gr.Button("开始召回", variant="primary")
                    # 示例输入
                    gr.Examples(
                        examples=[
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472119919229"],
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122722991"],
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472121659512"],
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472119919425"],
                        ],
                        inputs=[bug_recall_url],
                    )

                with gr.Column(scale=2):
                    bug_recall_chatbot = gr.Chatbot(
                        label="Bug召回分析",
                        avatar_images=("resources/img/user.jpeg", "resources/img/bot.jpeg"),
                        height=700,
                        show_share_button=True,
                        type="messages",
                    )
                    bug_recall_result = gr.File(
                        label="Bug召回结果",
                        height=50
                    )

            bug_recall_btn.click(
                fn=gen_bug_recall,
                inputs=[bug_recall_chatbot, bug_recall_url],
                outputs=[bug_recall_chatbot, bug_recall_result]
            )


if __name__ == "__main__":
    demo.queue(default_concurrency_limit=5).launch(
        server_name="0.0.0.0",
        server_port=8006,
        show_api=False
    )
