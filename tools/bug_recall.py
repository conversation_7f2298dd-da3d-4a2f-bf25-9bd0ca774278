import pandas as pd
import json
import re
import os
import sys
from datetime import datetime
from utils.logger.logger import Logging
from utils.llm.chat import chat_stream
from utils.llm.prompt import extract_bug_modules_prompt, analyze_bug_relevance_prompt, generate_bug_test_cases_prompt
from utils.tapd import TAPDUtils

# 添加bug配置路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from bug.config.bug_recall_config import get_config

logger = Logging().get_logger()


class BugRecallTool:
    """Bug召回工具类"""
    
    def __init__(self):
        self.tapd_utils = TAPDUtils()
    
    def gen_bug_file_path(self, story_name):
        """生成bug召回结果文件路径"""
        # 从配置获取输出目录和格式
        base_dir = get_config('output.base_dir') or "data/bug_recall"
        date_format = get_config('output.date_format') or "%Y%m%d"
        timestamp_format = get_config('output.timestamp_format') or "%Y%m%d_%H%M%S"
        file_format = get_config('output.file_format') or "xlsx"

        # 生成日期子目录
        date_subdir = datetime.now().strftime(date_format)
        case_dir = os.path.join(base_dir, date_subdir)

        # 确保输出目录存在
        os.makedirs(case_dir, exist_ok=True)

        # 替换文件名中的特殊字符
        safe_name = re.sub(r'[\\/:*?"<>|]', '_', story_name)

        # 生成唯一的文件名
        timestamp = datetime.now().strftime(timestamp_format)
        file_path = os.path.join(case_dir, f"{safe_name}_bug_recall_{timestamp}.{file_format}")

        # 确保父目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        return file_path
    
    def extract_bug_modules_from_story(self, story):
        """
        从需求文档中提取可能出现bug的功能模块
        :param story: 需求文档
        :return: 模块列表
        """
        try:
            # 从配置获取LLM参数
            max_tokens = get_config('llm.max_tokens') or 8096
            max_modules = get_config('prompt_templates.bug_modules.max_modules') or 8

            prompt = extract_bug_modules_prompt(story['description'])
            modules = chat_stream(prompt, max_tokens=max_tokens).choices[0].message.content
            logger.info(f"提取的bug风险模块: {modules}")

            # 清理响应内容（去除可能的代码标记）
            cleaned_content = re.sub(r'^```(json)?\s*|\s*```$', '', modules, flags=re.MULTILINE).strip()

            try:
                modules = json.loads(cleaned_content)
            except (ValueError, SyntaxError) as e:
                logger.error(f"解析bug模块失败：{str(e)}")
                return []

            # 限制模块数量
            modules = modules[:max_modules] if len(modules) > max_modules else modules
            logger.info(f"最终bug风险模块: {modules}")
            return modules
        except Exception as e:
            logger.error(f"提取bug模块时发生错误：{str(e)}")
            return []
    
    def analyze_bugs_relevance(self, story, bugs):
        """
        分析bug与需求的相关性
        :param story: 需求文档
        :param bugs: bug列表
        :return: 相关bug列表
        """
        try:
            if not bugs:
                return []
                
            bugs_json = json.dumps(bugs, ensure_ascii=False, indent=2)
            prompt = analyze_bug_relevance_prompt(story['description'], bugs_json)
            relevant_bugs = chat_stream(prompt, max_tokens=8096).choices[0].message.content
            logger.info(f"分析的相关bug: {relevant_bugs}")
            
            # 清理响应内容（去除可能的代码标记）
            cleaned_content = re.sub(r'^```(json)?\s*|\s*```$', '', relevant_bugs, flags=re.MULTILINE).strip()
            
            try:
                relevant_bugs = json.loads(cleaned_content)
            except (ValueError, SyntaxError) as e:
                logger.error(f"解析相关bug失败：{str(e)}")
                return []
                
            logger.info(f"最终相关bug: {relevant_bugs}")
            return relevant_bugs
        except Exception as e:
            logger.error(f"分析bug相关性时发生错误：{str(e)}")
            return []
    
    def generate_bug_test_cases(self, story, relevant_bugs):
        """
        基于相关bug生成测试用例建议
        :param story: 需求文档
        :param relevant_bugs: 相关bug列表
        :return: 测试用例列表
        """
        try:
            if not relevant_bugs:
                return []
                
            bugs_json = json.dumps(relevant_bugs, ensure_ascii=False, indent=2)
            prompt = generate_bug_test_cases_prompt(story['description'], bugs_json)
            test_cases = chat_stream(prompt, max_tokens=8096).choices[0].message.content
            logger.info(f"生成的bug测试用例: {test_cases}")
            
            # 清理响应内容（去除可能的代码标记）
            cleaned_content = re.sub(r'^```(json)?\s*|\s*```$', '', test_cases, flags=re.MULTILINE).strip()
            
            try:
                test_cases = json.loads(cleaned_content)
            except (ValueError, SyntaxError) as e:
                logger.error(f"解析bug测试用例失败：{str(e)}")
                return []
                
            logger.info(f"最终bug测试用例: {test_cases}")
            return test_cases
        except Exception as e:
            logger.error(f"生成bug测试用例时发生错误：{str(e)}")
            return []
    
    def save_bug_recall_to_excel(self, result_data, file_path):
        """
        将bug召回结果保存到Excel文件
        :param result_data: 结果数据
        :param file_path: 文件路径
        """
        try:
            # 创建Excel writer对象
            writer = pd.ExcelWriter(file_path, engine='xlsxwriter')
            
            # 需求信息工作表
            story_info = result_data['story_info']
            story_df = pd.DataFrame([story_info])
            story_df.to_excel(writer, sheet_name='需求信息', index=False)
            
            # 相关Bug工作表
            relevant_bugs = result_data['relevant_bugs']
            if relevant_bugs:
                bugs_df = pd.DataFrame(relevant_bugs)
                bugs_df.to_excel(writer, sheet_name='相关Bug', index=False)
            
            # 建议测试用例工作表
            test_cases = result_data['suggested_test_cases']
            if test_cases:
                # 处理测试步骤（将数组转换为字符串）
                processed_cases = []
                for case in test_cases:
                    processed_case = case.copy()
                    if 'test_steps' in processed_case and isinstance(processed_case['test_steps'], list):
                        processed_case['test_steps'] = '\n'.join([f"{i+1}. {step}" for i, step in enumerate(processed_case['test_steps'])])
                    processed_cases.append(processed_case)
                
                cases_df = pd.DataFrame(processed_cases)
                cases_df.to_excel(writer, sheet_name='建议测试用例', index=False)
            
            # 获取工作簿对象
            workbook = writer.book
            
            # 设置列宽格式
            format_wrap = workbook.add_format({'text_wrap': True})
            
            # 为每个工作表设置列宽
            for sheet_name in writer.sheets:
                worksheet = writer.sheets[sheet_name]
                worksheet.set_column('A:Z', 20, format_wrap)
            
            # 保存Excel文件
            writer.close()
            logger.info(f"Bug召回结果已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"保存Bug召回结果失败: {str(e)}")
    
    def recall_bugs_by_story_url(self, story_url):
        """
        根据需求链接召回相关BUG
        :param story_url: 需求链接
        :return: 召回结果字典
        """
        try:
            # 获取需求信息
            story = self.tapd_utils.get_story(story_url)
            if not story:
                logger.error("无法获取需求信息，请检查链接是否正确")
                return None
            
            logger.info(f"开始分析需求: {story['name']}")
            
            # 获取关联的bug列表
            bugs = self.tapd_utils.get_bugs_by_story_url(story_url)
            if not bugs:
                logger.warning("未找到与该需求关联的bug")
                return {
                    "story_info": {
                        "name": story.get('name', ''),
                        "description": story.get('description', ''),
                        "category": story.get('category_name', '')
                    },
                    "relevant_bugs": [],
                    "suggested_test_cases": []
                }
            
            logger.info(f"找到 {len(bugs)} 个关联的bug")
            
            # 分析bug相关性
            relevant_bugs = self.analyze_bugs_relevance(story, bugs)
            logger.info(f"找到 {len(relevant_bugs)} 个高度相关的bug")
            
            # 生成bug测试用例建议
            test_cases = self.generate_bug_test_cases(story, relevant_bugs)
            logger.info(f"生成 {len(test_cases)} 个建议测试用例")
            
            # 构建结果数据
            result_data = {
                "story_info": {
                    "name": story.get('name', ''),
                    "description": story.get('description', ''),
                    "category": story.get('category_name', '')
                },
                "relevant_bugs": relevant_bugs,
                "suggested_test_cases": test_cases
            }
            
            # 保存到Excel文件
            file_path = self.gen_bug_file_path(story['name'])
            self.save_bug_recall_to_excel(result_data, file_path)
            result_data['file_path'] = file_path
            
            return result_data
            
        except Exception as e:
            logger.error(f"Bug召回过程中发生错误：{str(e)}")
            return None


if __name__ == "__main__":
    # 测试用例
    bug_tool = BugRecallTool()
    story_url = "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472123851503"
    result = bug_tool.recall_bugs_by_story_url(story_url)
    if result:
        print("Bug召回完成")
        print(f"文件路径: {result.get('file_path', '')}")
        print(f"相关Bug数量: {len(result.get('relevant_bugs', []))}")
        print(f"建议测试用例数量: {len(result.get('suggested_test_cases', []))}")
    else:
        print("Bug召回失败")
